from google.cloud import firestore
from datetime import datetime, timedelta, timezone
from firebase.config import get_db
from fastapi import APIRouter, HTTPException, Query, Depends
from auth.deps import verify_firebase_token
import logging

router = APIRouter()
db = get_db()
logger = logging.getLogger(__name__)

def get_date_range(time_range: str):
    """Helper to convert time range string to start date"""
    now = datetime.now(timezone.utc)  # Use timezone-aware datetime
    if time_range == "7d":
        return now - timedelta(days=7)
    elif time_range == "30d":
        return now - timedelta(days=30)
    elif time_range == "90d":
        return now - timedelta(days=90)
    else:  # "all" or any other value
        return datetime(2000, 1, 1, tzinfo=timezone.utc)  # A date far in the past with timezone
async def adjust_trust_score(agent_id: str, adjustment: float):
    """Helper to adjust trust score within bounds"""
    agent_ref = db.collection("agents").document(agent_id)
    agent_doc = agent_ref.get()

    if not agent_doc.exists:
        raise Exception("Agent not found")

    current_score = agent_doc.to_dict().get("trust_score", 50.0)
    new_score = max(0, min(100, current_score + adjustment))
    agent_ref.update({"trust_score": new_score})
    return new_score


async def update_offer_spending(offer_ref, offer_data: dict, payout_amount: int, is_test: bool):
    """Update offer total_spent and budget allocation"""
    logger.info(f"💸 Updating offer spending: amount={payout_amount}, is_test={is_test}")

    # Get current total_spent structure
    total_spent = offer_data.get("total_spent", {})
    if not isinstance(total_spent, dict):
        # Convert old format to new format
        total_spent = {"test": 0, "production": 0, "total": total_spent if isinstance(total_spent, (int, float)) else 0}

    # Ensure all fields exist
    total_spent.setdefault("test", 0)
    total_spent.setdefault("production", 0)
    total_spent.setdefault("total", 0)

    # Update spending
    update_data = {
        "total_spent.total": firestore.Increment(payout_amount),
        "updated_at": firestore.SERVER_TIMESTAMP
    }

    if is_test:
        update_data["total_spent.test"] = firestore.Increment(payout_amount)
        logger.info(f"📊 Test spending updated: +{payout_amount}")
    else:
        update_data["total_spent.production"] = firestore.Increment(payout_amount)
        # Deduct from budget only for production conversions
        update_data["total_budget_allocated"] = firestore.Increment(-payout_amount)
        logger.info(f"📊 Production spending updated: +{payout_amount}, budget reduced: -{payout_amount}")

    offer_ref.update(update_data)


async def create_wallet_transactions(brand_id: str, agent_id: str, user_id: str, payout_amount: int,
                                   agent_earning: int, user_earning: int, protocol_fee: int,
                                   offer_id: str, is_test: bool):
    """Create wallet transactions for brand, agent, and user"""
    logger.info(f"💳 Creating wallet transactions: brand={brand_id}, agent={agent_id}, user={user_id}")

    timestamp = datetime.now(timezone.utc)

    # Brand wallet transaction (deduction)
    brand_transaction = {
        "type": "conversion_payout",
        "amount": -payout_amount,  # Negative for deduction
        "currency": "USD",
        "offer_id": offer_id,
        "agent_id": agent_id,
        "user_id": user_id,
        "is_test": is_test,
        "timestamp": timestamp,
        "status": "completed",
        "description": f"Conversion payout for offer {offer_id}"
    }

    # Add brand transaction
    db.collection("wallets").document(brand_id).collection("transactions").add(brand_transaction)

    # Update brand wallet balance
    db.collection("wallets").document(brand_id).update({
        "total_budget_allocated": firestore.Increment(-payout_amount) if not is_test else firestore.Increment(0),
        "updated_at": firestore.SERVER_TIMESTAMP
    })
    logger.info(f"💰 Brand wallet updated: -{payout_amount}")

    # Agent wallet transaction (credit)
    if agent_earning > 0:
        agent_transaction = {
            "type": "conversion_payout",
            "amount": agent_earning,  # Positive for credit
            "currency": "USD",
            "offer_id": offer_id,
            "brand_id": brand_id,
            "user_id": user_id,
            "is_test": is_test,
            "timestamp": timestamp,
            "status": "completed",
            "description": f"Agent earnings from conversion {offer_id}"
        }

        db.collection("wallets").document(agent_id).collection("transactions").add(agent_transaction)

        # Update agent wallet balance
        db.collection("wallets").document(agent_id).update({
            "total_earnings": firestore.Increment(agent_earning),
            "updated_at": firestore.SERVER_TIMESTAMP
        })
        logger.info(f"💰 Agent wallet updated: +{agent_earning}")

    # User wallet transaction (credit)
    if user_id and user_earning > 0:
        user_transaction = {
            "type": "conversion_payout",
            "amount": user_earning,  # Positive for credit
            "currency": "USD",
            "offer_id": offer_id,
            "brand_id": brand_id,
            "agent_id": agent_id,
            "is_test": is_test,
            "timestamp": timestamp,
            "status": "completed",
            "description": f"User earnings from conversion {offer_id}"
        }

        db.collection("wallets").document(user_id).collection("transactions").add(user_transaction)

        # Update user wallet balance
        db.collection("wallets").document(user_id).update({
            "total_earnings": firestore.Increment(user_earning),
            "updated_at": firestore.SERVER_TIMESTAMP
        })
        logger.info(f"💰 User wallet updated: +{user_earning}")


async def update_conversion_status(conversion_id: str, click_id: str, status: str):
    """Update conversion and click status"""
    logger.info(f"🔄 Updating status to '{status}': conversion_id={conversion_id}, click_id={click_id}")

    # Update conversion status
    if conversion_id:
        try:
            db.collection("conversions").document(conversion_id).update({
                "status": status,
                "updated_at": firestore.SERVER_TIMESTAMP
            })
            logger.info(f"✅ Conversion status updated: {conversion_id} -> {status}")
        except Exception as e:
            logger.error(f"Failed to update conversion status: {str(e)}")

    # Update click status
    if click_id:
        try:
            click_status = "converted" if status == "completed" else "pending"
            update_data = {
                "status": click_status,
                "updated_at": firestore.SERVER_TIMESTAMP
            }

            if status == "completed" and conversion_id:
                update_data["conversion_id"] = conversion_id
                update_data["converted_at"] = firestore.SERVER_TIMESTAMP

            db.collection("clicks").document(click_id).update(update_data)
            logger.info(f"✅ Click status updated: {click_id} -> {click_status}")
        except Exception as e:
            logger.error(f"Failed to update click status: {str(e)}")
async def record_agent_earning_from_conversion(conversion_event, click_id: str = None):
    """
    Record agent earnings from conversion and handle all related updates:
    - Update offer total_spent and budget
    - Create wallet transactions
    - Update conversion and click status
    """
    event_data = conversion_event.model_dump()
    offer_id = event_data["offer_id"]
    agent_id = event_data.get("agent_id")
    user_id = event_data.get("user_id")
    intent_type = event_data.get("intent_type", "generic")
    is_test = event_data.get("is_test", False)

    logger.info(f"🎯 Recording earnings for conversion: offer_id={offer_id}, agent_id={agent_id}, user_id={user_id}, is_test={is_test}")

    if not agent_id:
        logger.error("Agent ID is required to record earnings")
        raise Exception("Agent ID is required to record earnings")

    # Start transaction for atomic operations
    transaction = db.transaction()

    try:
        # Get offer document
        offer_ref = db.collection("offers").document(offer_id)
        offer_doc = offer_ref.get()
        if not offer_doc.exists:
            logger.error(f"Offer not found: {offer_id}")
            raise Exception("Offer not found")

        offer = offer_doc.to_dict()
        brand_id = offer.get("brand_id")
        payout_info = offer.get("payout", {})
        payout_model = payout_info.get("model", "CPA")
        payout_amount = int(payout_info.get("amount", 0))  # cents
        currency = payout_info.get("currency", "USD")

        logger.info(f"💰 Processing payout: amount={payout_amount} cents, currency={currency}, model={payout_model}")

        if payout_model != "CPA":
            logger.error(f"Unsupported payout model: {payout_model}")
            raise Exception("Only CPA model is supported in MVP")

        # Use the reward split from the conversion event (which was calculated dynamically)
        reward_split = event_data.get("reward_split", {"agent": 60, "user": 10, "admesh": 30})

        logger.info(f"📊 Using dynamic reward split: {reward_split}")

        total_shares = sum(reward_split.values()) or 1

        agent_earning = round(payout_amount * reward_split.get("agent", 0) / total_shares)
        user_earning = round(payout_amount * reward_split.get("user", 0) / total_shares)
        protocol_fee = payout_amount - agent_earning - user_earning

        logger.info(f"💵 Earnings breakdown: agent={agent_earning}, user={user_earning}, protocol={protocol_fee}")

        # Check budget availability for production conversions
        if not is_test:
            total_budget_allocated = offer.get("total_budget_allocated", 0)
            if total_budget_allocated < payout_amount:
                logger.error(f"❌ Insufficient budget: allocated={total_budget_allocated}, required={payout_amount}")
                raise Exception(f"Insufficient budget for conversion. Available: {total_budget_allocated}, Required: {payout_amount}")
            logger.info(f"✅ Budget check passed: allocated={total_budget_allocated}, required={payout_amount}")

        # Update offer total_spent and budget
        await update_offer_spending(offer_ref, offer, payout_amount, is_test)

        # Create wallet transactions for brand, agent, and user
        await create_wallet_transactions(brand_id, agent_id, user_id, payout_amount, agent_earning, user_earning, protocol_fee, offer_id, is_test)

        timestamp = event_data.get("timestamp")
        if isinstance(timestamp, str):
            try:
                timestamp = datetime.fromisoformat(timestamp)
            except Exception:
                timestamp = datetime.now(timezone.utc)
        elif not timestamp:
            timestamp = datetime.now(timezone.utc)

        earning_doc = {
            "click_id": click_id,
            "brand_id": brand_id,
            "agent_id": agent_id,
            "user_id": user_id,
            "offer_id": offer_id,
            "amount": payout_amount,
            "currency": currency,
            "agent_earning": agent_earning,
            "user_earning": user_earning,
            "protocol_fee": protocol_fee,
            "event_type": event_data.get("event_type", "conversion"),
            "session_id": event_data.get("session_id"),
            "recommendation_id": event_data.get("recommendation_id"),
            "intent_type": intent_type,
            "timestamp": timestamp.isoformat(),
            "recorded_at": datetime.now(timezone.utc).isoformat(),
            "reward_split": reward_split,
            "payout": payout_info,  # Store entire payout object
            "is_test": is_test,
            "status": "completed"  # Mark as completed since all operations succeeded
        }

        # Add metadata about user share handling
        if not user_id:
            earning_doc["user_share_added_to_admesh"] = True
            logger.info(f"👤 User share added to AdMesh for conversion {offer_id}")

        # Store earnings record
        earnings_ref = db.collection("earnings").add(earning_doc)
        logger.info(f"💾 Earnings record created: {earnings_ref[1].id}")

        # Update Agent stats
        db.collection("agents").document(agent_id).set({
            "total_earnings": firestore.Increment(agent_earning),
            "conversions": firestore.Increment(1),
            f"earnings_by_intent.{intent_type}": firestore.Increment(agent_earning)
        }, merge=True)
        logger.info(f"📈 Agent stats updated: {agent_id}")

        # Update User stats
        if user_id:
            db.collection("users").document(user_id).set({
                "total_earnings": firestore.Increment(user_earning),
                "intent_conversions": firestore.Increment(1),
                f"earnings_by_intent.{intent_type}": firestore.Increment(user_earning)
            }, merge=True)
            logger.info(f"📈 User stats updated: {user_id}")

        # Update conversion status to completed
        await update_conversion_status(event_data.get("conversion_id"), click_id, "completed")

        # Adjust agent trust score slightly upward
        try:
            await adjust_trust_score(agent_id, adjustment=+0.25)
            logger.info(f"⭐ Trust score adjusted for agent: {agent_id}")
        except Exception as e:
            logger.warning(f"Could not adjust trust score: {e}")

        logger.info(f"✅ Conversion earnings processing completed successfully")
        return earning_doc

    except Exception as e:
        logger.error(f"❌ Error processing conversion earnings: {str(e)}")
        # Update conversion status to pending due to error
        try:
            await update_conversion_status(event_data.get("conversion_id"), click_id, "pending")
        except Exception as status_error:
            logger.error(f"Failed to update conversion status to pending: {str(status_error)}")
        raise e

@router.get("/earnings/{agent_id}")
async def get_agent_earnings(
    agent_id: str,
    time_range: str = Query("30d", description="Time range: 7d, 30d, 90d, or all"),
    decoded_token = Depends(verify_firebase_token)
):
    """List all earnings for a given agent with time range filtering"""
    try:
        # Verify the user is requesting their own earnings or is an admin
        if decoded_token["uid"] != agent_id and not decoded_token.get("admin", False):
            raise HTTPException(status_code=403, detail="You can only access your own earnings")
        # Get the start date based on time range
        start_date = get_date_range(time_range)

        # Convert start_date to ISO format string for comparison
        start_date_iso = start_date.isoformat()

        # Query earnings for this agent with time filter
        earnings_ref = db.collection("earnings")

        try:
            # Try to use the composite index if it exists
            query = (
                earnings_ref
                .where(filter=firestore.FieldFilter("agent_id", "==", agent_id))
                .where(filter=firestore.FieldFilter("timestamp", ">=", start_date_iso))
                .order_by("timestamp", direction=firestore.Query.DESCENDING)
            )
            earnings_docs = query.stream()
        except Exception as index_error:
            # If the composite index doesn't exist, fall back to filtering in memory
            logger.warning(f"Composite index error: {str(index_error)}")
            logger.warning("Falling back to simpler query without time filtering")

            # Get all earnings for this agent
            query = earnings_ref.where(filter=firestore.FieldFilter("agent_id", "==", agent_id))
            all_docs = list(query.stream())

            # Filter by date in memory
            earnings_docs = [
                doc for doc in all_docs
                if doc.to_dict().get("timestamp") and doc.to_dict().get("timestamp") >= start_date_iso
            ]

            # Sort by timestamp descending
            earnings_docs.sort(
                key=lambda doc: doc.to_dict().get("timestamp", ""),
                reverse=True
            )

        # Convert to dict and add document ID
        earnings_list = []
        for doc in earnings_docs:
            earning_data = doc.to_dict()
            earning_data["id"] = doc.id  # Add document ID
            earnings_list.append(earning_data)

        return {"earnings": earnings_list}
    except Exception as e:
        logger.exception(f"Failed to fetch agent earnings: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/user/{user_id}")
async def get_user_earnings(
    user_id: str,
    time_range: str = Query("30d", description="Time range: 7d, 30d, 90d, or all"),
    decoded_token = Depends(verify_firebase_token)
):
    """List all earnings for a given user with time range filtering"""
    try:
        # Ensure user is accessing their own data or is admin
        if decoded_token["uid"] != user_id and not decoded_token.get("admin", False):
            raise HTTPException(status_code=403, detail="You can only access your own earnings")

        start_date = get_date_range(time_range)

        # Convert start_date to ISO format string for comparison
        start_date_iso = start_date.isoformat()

        # Query earnings with composite index: user_id + timestamp
        earnings_query = (
            db.collection("earnings")
            .where(filter=firestore.FieldFilter("user_id", "==", user_id))
            .where(filter=firestore.FieldFilter("timestamp", ">=", start_date_iso))
            .order_by("timestamp", direction=firestore.Query.DESCENDING)
        )

        earnings_docs = earnings_query.stream()

        earnings_list = []
        for doc in earnings_docs:
            data = doc.to_dict()
            data["id"] = doc.id
            earnings_list.append(data)

        return {"earnings": earnings_list}

    except Exception as e:
        error_msg = f"Failed to fetch user earnings: {str(e)}"
        logger.exception(error_msg)
        # Include more details in the error response
        raise HTTPException(
            status_code=500,
            detail={
                "message": "Failed to fetch earnings",
                "error": str(e),
                "user_id": user_id,
                "time_range": time_range
            }
        )

